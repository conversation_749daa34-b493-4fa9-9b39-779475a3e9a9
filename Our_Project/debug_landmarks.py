#!/usr/bin/env python3
"""
Debug script to test facial landmark detection and visualization
"""

import cv2
import numpy as np
import os
import sys
import json
from pathlib import Path

# Add python directory to path
sys.path.append(str(Path(__file__).parent / 'python'))

from avatar_processor import AvatarProcessor

def visualize_landmarks(image_path, landmarks_data, output_path):
    """
    Visualize facial landmarks on the image
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"Could not load image: {image_path}")
        return False
    
    # Draw all landmarks
    all_landmarks = landmarks_data['all_landmarks']
    for i, (x, y) in enumerate(all_landmarks):
        cv2.circle(image, (x, y), 1, (0, 255, 0), -1)
        if i % 50 == 0:  # Label every 50th landmark
            cv2.putText(image, str(i), (x+2, y-2), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 0), 1)
    
    # Highlight lip landmarks in red
    lip_landmarks = landmarks_data['lip_landmarks']
    for i, (x, y) in enumerate(lip_landmarks):
        cv2.circle(image, (x, y), 2, (0, 0, 255), -1)
        cv2.putText(image, f"L{i}", (x+3, y-3), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 255), 1)
    
    # Draw lip contour
    if len(lip_landmarks) > 0:
        lip_points = np.array(lip_landmarks, dtype=np.int32)
        cv2.polylines(image, [lip_points], True, (255, 0, 0), 2)
    
    # Save result
    cv2.imwrite(output_path, image)
    print(f"Visualization saved to: {output_path}")
    return True

def test_avatar_processing():
    """
    Test the avatar processing pipeline
    """
    print("🔍 Testing Avatar Processing Pipeline...")
    
    # Initialize processor
    processor = AvatarProcessor()
    
    # Find the most recent avatar image
    upload_dir = Path("python/uploads")
    if not upload_dir.exists():
        print("❌ Upload directory not found")
        return
    
    # Get most recent avatar file
    avatar_files = list(upload_dir.glob("*saved-avatar.jpg"))
    if not avatar_files:
        print("❌ No avatar files found")
        return
    
    latest_avatar = max(avatar_files, key=lambda f: f.stat().st_mtime)
    print(f"📸 Testing with avatar: {latest_avatar}")
    
    # Test landmark detection
    print("🎯 Detecting facial landmarks...")
    landmarks_data = processor.detect_facial_landmarks(str(latest_avatar))
    
    if not landmarks_data:
        print("❌ No facial landmarks detected!")
        return
    
    print("✅ Facial landmarks detected successfully!")
    print(f"   - Total landmarks: {len(landmarks_data['all_landmarks'])}")
    print(f"   - Lip landmarks: {len(landmarks_data['lip_landmarks'])}")
    print(f"   - Image shape: {landmarks_data['image_shape']}")
    
    # Create visualization
    output_path = "debug_landmarks_visualization.jpg"
    if visualize_landmarks(str(latest_avatar), landmarks_data, output_path):
        print(f"✅ Landmark visualization created: {output_path}")
    
    # Test viseme warping
    print("🎭 Testing viseme warping...")
    original_image = cv2.imread(str(latest_avatar))
    
    test_visemes = ['A', 'E', 'O', 'B', 'M']
    for viseme in test_visemes:
        warped = processor.warp_mouth_for_viseme(original_image, landmarks_data, viseme, 0.8)
        output_file = f"debug_viseme_{viseme}.jpg"
        cv2.imwrite(output_file, warped)
        print(f"   - Created {viseme} viseme: {output_file}")
    
    print("✅ Avatar processing test completed!")

def check_existing_landmarks():
    """
    Check existing landmark files
    """
    print("📁 Checking existing landmark files...")
    
    processed_dir = Path("python/processed")
    if not processed_dir.exists():
        print("❌ Processed directory not found")
        return
    
    landmark_files = list(processed_dir.glob("*_landmarks.json"))
    print(f"📊 Found {len(landmark_files)} landmark files")
    
    if landmark_files:
        # Check the most recent one
        latest_file = max(landmark_files, key=lambda f: f.stat().st_mtime)
        print(f"🔍 Examining latest: {latest_file.name}")
        
        try:
            with open(latest_file, 'r') as f:
                data = json.load(f)
            
            print(f"   - All landmarks: {len(data.get('all_landmarks', []))}")
            print(f"   - Lip landmarks: {len(data.get('lip_landmarks', []))}")
            print(f"   - Image shape: {data.get('image_shape', 'Unknown')}")
            
            # Check if landmarks look reasonable
            if data.get('lip_landmarks'):
                lip_coords = data['lip_landmarks']
                x_coords = [coord[0] for coord in lip_coords]
                y_coords = [coord[1] for coord in lip_coords]
                
                print(f"   - Lip X range: {min(x_coords)} - {max(x_coords)}")
                print(f"   - Lip Y range: {min(y_coords)} - {max(y_coords)}")
                
                if min(x_coords) >= 0 and min(y_coords) >= 0:
                    print("✅ Landmark coordinates look valid")
                else:
                    print("⚠️  Some landmark coordinates are negative")
            
        except Exception as e:
            print(f"❌ Error reading landmark file: {e}")

def main():
    """
    Main debug function
    """
    print("🐛 Avatar Landmark Debug Tool")
    print("=" * 40)
    
    # Check existing landmarks
    check_existing_landmarks()
    print()
    
    # Test avatar processing
    test_avatar_processing()
    
    print("\n🎯 Debug Summary:")
    print("- Check the generated visualization files")
    print("- Look for any error messages above")
    print("- Verify that lip landmarks are properly positioned")

if __name__ == "__main__":
    main()
