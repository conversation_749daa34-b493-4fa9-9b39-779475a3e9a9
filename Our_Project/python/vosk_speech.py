#!/usr/bin/env python3
"""
Vosk Speech Recognition Integration
Provides offline speech-to-text using Vosk models
"""

import json
import wave
import vosk
import sys
import os
import subprocess
import tempfile
from pathlib import Path

class VoskSpeechRecognizer:
    def __init__(self, model_path=None):
        """Initialize Vosk speech recognizer with model"""
        if model_path is None:
            # Default to the small English model we downloaded
            project_root = Path(__file__).parent.parent
            model_path = project_root / "models" / "vosk-model-small-en-us-0.15"
        
        self.model_path = str(model_path)
        self.model = None
        self.recognizer = None
        
        # Initialize model
        self._load_model()
    
    def _load_model(self):
        """Load the Vosk model"""
        try:
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"Vosk model not found at: {self.model_path}")

            print(f"Loading Vosk model from: {self.model_path}")
            self.model = vosk.Model(self.model_path)
            print("Vosk model loaded successfully")

        except Exception as e:
            print(f"Error loading Vosk model: {e}")
            raise

    def _convert_audio_to_wav(self, input_path):
        """
        Convert audio file to WAV format compatible with Vosk
        Returns path to converted file (temporary file)
        """
        try:
            # Create temporary file for converted audio
            temp_fd, temp_path = tempfile.mkstemp(suffix='.wav')
            os.close(temp_fd)  # Close file descriptor, we'll use the path

            # Use ffmpeg to convert to 16kHz mono WAV
            cmd = [
                'ffmpeg', '-i', input_path,
                '-ar', '16000',  # 16kHz sample rate
                '-ac', '1',      # Mono
                '-f', 'wav',     # WAV format
                '-y',            # Overwrite output file
                temp_path
            ]

            print(f"Converting audio: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                raise RuntimeError(f"FFmpeg conversion failed: {result.stderr}")

            print(f"Audio converted successfully to: {temp_path}")
            return temp_path

        except Exception as e:
            print(f"Error converting audio: {e}")
            raise
    
    def transcribe_audio_file(self, audio_file_path):
        """
        Transcribe an audio file using Vosk

        Args:
            audio_file_path (str): Path to the audio file (any format supported by ffmpeg)

        Returns:
            str: Transcribed text
        """
        converted_file = None
        try:
            # Check if file exists
            if not os.path.exists(audio_file_path):
                raise FileNotFoundError(f"Audio file not found: {audio_file_path}")

            # Convert audio to compatible format
            converted_file = self._convert_audio_to_wav(audio_file_path)

            # Open the converted audio file
            with wave.open(converted_file, "rb") as wf:
                # Check audio format
                if wf.getnchannels() != 1:
                    raise ValueError("Audio file must be mono (1 channel)")
                
                sample_rate = wf.getframerate()
                print(f"Processing audio file: {audio_file_path}")
                print(f"Sample rate: {sample_rate} Hz")
                
                # Create recognizer for this sample rate
                recognizer = vosk.KaldiRecognizer(self.model, sample_rate)
                recognizer.SetWords(True)  # Enable word-level timestamps
                
                # Process audio in chunks
                transcription_parts = []
                
                while True:
                    data = wf.readframes(4000)  # Read 4000 frames at a time
                    if len(data) == 0:
                        break
                    
                    if recognizer.AcceptWaveform(data):
                        # Get partial result
                        result = json.loads(recognizer.Result())
                        if result.get("text"):
                            transcription_parts.append(result["text"])
                
                # Get final result
                final_result = json.loads(recognizer.FinalResult())
                if final_result.get("text"):
                    transcription_parts.append(final_result["text"])
                
                # Combine all parts
                full_transcription = " ".join(transcription_parts).strip()
                
                print(f"Transcription completed: '{full_transcription}'")
                return full_transcription

        except Exception as e:
            print(f"Error transcribing audio: {e}")
            raise
        finally:
            # Clean up temporary file
            if converted_file and os.path.exists(converted_file):
                try:
                    os.unlink(converted_file)
                    print(f"Cleaned up temporary file: {converted_file}")
                except Exception as cleanup_error:
                    print(f"Warning: Could not clean up temporary file {converted_file}: {cleanup_error}")
    
    def is_available(self):
        """Check if Vosk is available and working"""
        return self.model is not None

def main():
    """Command line interface for testing"""
    if len(sys.argv) != 2:
        print("Usage: python vosk_speech.py <audio_file.wav>")
        sys.exit(1)
    
    audio_file = sys.argv[1]
    
    try:
        # Initialize recognizer
        recognizer = VoskSpeechRecognizer()
        
        # Transcribe audio
        transcription = recognizer.transcribe_audio_file(audio_file)
        
        if transcription:
            print(f"Transcription: {transcription}")
        else:
            print("No speech detected in audio file")
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
