// Avatar rendering and animation using HTML5 Canvas
class AvatarRenderer {
    constructor() {
        this.canvas = document.getElementById('avatarCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.avatarImage = null;
        this.originalImageData = null;
        this.isAnimating = false;
        this.animationFrameId = null;
        
        // Animation properties
        this.currentFrame = 0;
        this.animationFrames = [];
        this.frameRate = 30; // FPS
        this.frameDuration = 1000 / this.frameRate;
        this.lastFrameTime = 0;
    }

    async loadAvatar(imageFile) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = async () => {
                this.avatarImage = img;
                this.setupCanvas();
                this.drawStaticAvatar();

                // Process avatar with Python server for facial landmarks
                try {
                    await this.processAvatarWithPython(imageFile);
                } catch (error) {
                    console.warn('Failed to process avatar with Python server:', error);
                    // Continue without facial processing
                }

                resolve();
            };
            img.onerror = reject;
            img.src = URL.createObjectURL(imageFile);
        });
    }

    setupCanvas() {
        if (!this.avatarImage) return;

        // Set canvas size to maintain aspect ratio
        const maxSize = 400;
        const aspectRatio = this.avatarImage.width / this.avatarImage.height;
        
        if (aspectRatio > 1) {
            this.canvas.width = maxSize;
            this.canvas.height = maxSize / aspectRatio;
        } else {
            this.canvas.width = maxSize * aspectRatio;
            this.canvas.height = maxSize;
        }

        // Store original image data
        this.drawStaticAvatar();
        this.originalImageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    }

    drawStaticAvatar() {
        if (!this.avatarImage) return;

        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.drawImage(this.avatarImage, 0, 0, this.canvas.width, this.canvas.height);
    }

    async playAnimation(animationData, audioPath) {
        if (!animationData || animationData.length === 0) {
            console.warn('No animation data provided');
            return;
        }

        try {
            // Generate proper lip sync frames if we have facial landmarks
            const lipSyncFrames = await this.generateLipSyncFrames(animationData, audioPath);

            // Load and play audio - construct full URL for audio file
            const audioUrl = `http://localhost:3001/audio/${audioPath}`;
            console.log('Playing audio:', audioUrl);
            const audio = new Audio(audioUrl);

            // Prepare animation frames
            this.animationFrames = lipSyncFrames;
            this.currentFrame = 0;
            this.isAnimating = true;

            // Start animation
            const startTime = performance.now();
            this.lastFrameTime = startTime;

            // Add error handling for audio
            audio.addEventListener('error', (e) => {
                console.error('Audio loading error:', e);
                console.error('Audio error details:', audio.error);
            });

            audio.addEventListener('canplaythrough', () => {
                console.log('Audio can play through');
            });

            // Play audio with error handling
            try {
                await audio.play();
                console.log('Audio playback started successfully');
            } catch (e) {
                console.error('Audio play failed:', e);
                // Continue with animation even if audio fails
            }

            // Start proper lip sync animation if we have processed frames
            if (this.facialLandmarks && lipSyncFrames !== animationData) {
                this.startLipSyncAnimation();
            } else {
                // Fallback to simple talking animation
                this.startTalkingAnimation();
            }

            // Stop animation when audio ends
            audio.addEventListener('ended', () => {
                this.stopAllAnimation();
            });

        } catch (error) {
            console.error('Animation playback error:', error);
            this.stopAllAnimation();
        }
    }

    startTalkingAnimation() {
        if (this.talkingInterval) {
            clearInterval(this.talkingInterval);
        }

        this.isTalking = true;
        let talkPhase = 0;

        console.log('Starting talking animation');

        // Create visible talking animation
        this.talkingInterval = setInterval(() => {
            if (!this.isTalking) return;

            talkPhase += 0.5;
            this.drawTalkingAvatar(talkPhase);
        }, 100); // 10 FPS for visible animation
    }

    stopTalkingAnimation() {
        this.isTalking = false;
        if (this.talkingInterval) {
            clearInterval(this.talkingInterval);
            this.talkingInterval = null;
        }
        // Return to static avatar
        this.drawStaticAvatar();
    }

    startLipSyncAnimation() {
        if (this.lipSyncInterval) {
            clearInterval(this.lipSyncInterval);
        }

        this.isLipSyncing = true;
        let frameIndex = 0;

        console.log('Starting lip sync animation with', this.animationFrames.length, 'frames');

        // Create precise lip sync animation
        this.lipSyncInterval = setInterval(() => {
            if (!this.isLipSyncing || frameIndex >= this.animationFrames.length) {
                this.stopLipSyncAnimation();
                return;
            }

            const frameData = this.animationFrames[frameIndex];
            this.drawLipSyncFrame(frameData);
            frameIndex++;
        }, 33); // ~30 FPS
    }

    stopLipSyncAnimation() {
        this.isLipSyncing = false;
        if (this.lipSyncInterval) {
            clearInterval(this.lipSyncInterval);
            this.lipSyncInterval = null;
        }
        // Return to static avatar
        this.drawStaticAvatar();
    }

    stopAllAnimation() {
        this.stopTalkingAnimation();
        this.stopLipSyncAnimation();
        this.stopAnimation();
    }

    drawTalkingAvatar(talkPhase) {
        if (!this.avatarImage) return;

        // Clear and redraw everything
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Calculate animation values - make them more dramatic
        const intensity = Math.abs(Math.sin(talkPhase)) * 0.9 + 0.1; // 0.1 to 1.0
        const scale = 1 + intensity * 0.15; // More dramatic scaling
        const bounceY = Math.sin(talkPhase * 2) * 8; // Vertical bounce
        const wiggleX = Math.sin(talkPhase * 3) * 4; // Horizontal wiggle

        // Draw background glow effect
        this.ctx.save();
        this.ctx.shadowColor = '#10b981';
        this.ctx.shadowBlur = 20 * intensity;
        this.ctx.fillStyle = `rgba(16, 185, 129, ${intensity * 0.1})`;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.restore();

        // Save context for transformations
        this.ctx.save();

        // Move to center for scaling and add movement
        this.ctx.translate(
            this.canvas.width / 2 + wiggleX,
            this.canvas.height / 2 + bounceY
        );
        this.ctx.scale(scale, scale);

        // Draw avatar with transformations
        this.ctx.drawImage(
            this.avatarImage,
            -this.canvas.width / 2,
            -this.canvas.height / 2,
            this.canvas.width,
            this.canvas.height
        );

        this.ctx.restore();

        // Add simulated mouth animation overlay
        this.drawSimulatedMouthAnimation(talkPhase, intensity);

        // Add VERY visible talking indicators
        this.ctx.save();

        // Thick pulsing border
        this.ctx.strokeStyle = `rgba(16, 185, 129, ${intensity})`;
        this.ctx.lineWidth = 8;
        this.ctx.strokeRect(4, 4, this.canvas.width - 8, this.canvas.height - 8);

        // Large speaking indicator with background
        this.ctx.fillStyle = `rgba(0, 0, 0, 0.7)`;
        this.ctx.fillRect(0, 0, this.canvas.width, 40);

        this.ctx.fillStyle = `rgba(16, 185, 129, ${intensity})`;
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('🗣️ SIMPLE ANIMATION', this.canvas.width / 2, 28);

        // Multiple sound wave rings
        for (let i = 0; i < 5; i++) {
            const waveRadius = 30 + i * 20 + intensity * 15;
            this.ctx.beginPath();
            this.ctx.arc(this.canvas.width / 2, this.canvas.height / 2, waveRadius, 0, 2 * Math.PI);
            this.ctx.strokeStyle = `rgba(16, 185, 129, ${(1 - i * 0.15) * intensity * 0.4})`;
            this.ctx.lineWidth = 3;
            this.ctx.stroke();
        }

        // Corner indicators
        const cornerSize = 20 * intensity;
        this.ctx.fillStyle = `rgba(16, 185, 129, ${intensity})`;
        this.ctx.fillRect(0, 0, cornerSize, cornerSize);
        this.ctx.fillRect(this.canvas.width - cornerSize, 0, cornerSize, cornerSize);
        this.ctx.fillRect(0, this.canvas.height - cornerSize, cornerSize, cornerSize);
        this.ctx.fillRect(this.canvas.width - cornerSize, this.canvas.height - cornerSize, cornerSize, cornerSize);

        this.ctx.restore();
    }

    drawSimulatedMouthAnimation(talkPhase, intensity) {
        // Add a simulated mouth animation even without facial landmarks
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height * 0.72;

        // Cycle through different mouth shapes
        const shapeIndex = Math.floor((talkPhase * 2) % 6);
        const shapes = ['A', 'E', 'O', 'U', 'I', 'X'];
        const currentShape = shapes[shapeIndex];

        this.ctx.save();
        this.ctx.globalAlpha = 0.8;

        // Bright color for visibility
        this.ctx.fillStyle = '#ff4444';
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 3;

        // Draw the mouth shape
        const baseSize = 35;
        const size = baseSize * (0.7 + intensity * 0.6);

        this.ctx.beginPath();
        switch(currentShape) {
            case 'A':
                this.ctx.ellipse(centerX, centerY, size * 1.5, size * 1.0, 0, 0, 2 * Math.PI);
                break;
            case 'E':
                this.ctx.ellipse(centerX, centerY, size * 2.0, size * 0.5, 0, 0, 2 * Math.PI);
                break;
            case 'O':
                this.ctx.ellipse(centerX, centerY, size * 0.8, size * 0.8, 0, 0, 2 * Math.PI);
                break;
            case 'U':
                this.ctx.ellipse(centerX, centerY, size * 0.6, size * 0.6, 0, 0, 2 * Math.PI);
                break;
            case 'I':
                this.ctx.ellipse(centerX, centerY, size * 1.8, size * 0.3, 0, 0, 2 * Math.PI);
                break;
            default:
                this.ctx.ellipse(centerX, centerY, size * 1.2, size * 0.4, 0, 0, 2 * Math.PI);
        }

        this.ctx.fill();
        this.ctx.stroke();

        // Add shape label
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 14px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(currentShape, centerX, centerY - size - 10);

        this.ctx.restore();
    }

    animateFrame(currentTime) {
        if (!this.isAnimating) return;

        // Calculate if it's time for next frame
        const deltaTime = currentTime - this.lastFrameTime;
        
        if (deltaTime >= this.frameDuration) {
            this.renderFrame();
            this.currentFrame++;
            this.lastFrameTime = currentTime;

            // Check if animation is complete
            if (this.currentFrame >= this.animationFrames.length) {
                this.stopAnimation();
                return;
            }
        }

        this.animationFrameId = requestAnimationFrame((time) => this.animateFrame(time));
    }

    renderFrame() {
        if (!this.originalImageData || this.currentFrame >= this.animationFrames.length) {
            return;
        }

        // Get current frame data
        const frameData = this.animationFrames[this.currentFrame];
        
        // Restore original image
        this.ctx.putImageData(this.originalImageData, 0, 0);

        // Apply frame transformations
        this.applyFrameTransformations(frameData);
    }

    applyFrameTransformations(frameData) {
        // This is a simplified version - in the full implementation,
        // this would apply the actual lip sync transformations
        // based on the viseme data from Rhubarb
        
        if (!frameData || !frameData.viseme) return;

        const viseme = frameData.viseme;
        const intensity = frameData.intensity || 1.0;

        // Apply basic mouth shape transformations based on viseme
        this.applyMouthShape(viseme, intensity);
    }

    applyMouthShape(viseme, intensity) {
        // Simplified mouth shape application
        // In the full implementation, this would use the facial landmarks
        // and OpenCV-style transformations
        
        const mouthShapes = {
            'A': { openness: 0.8, width: 1.0 },
            'B': { openness: 0.0, width: 0.8 },
            'C': { openness: 0.6, width: 0.9 },
            'D': { openness: 0.4, width: 1.0 },
            'E': { openness: 0.3, width: 1.1 },
            'F': { openness: 0.1, width: 0.9 },
            'G': { openness: 0.2, width: 0.8 },
            'H': { openness: 0.3, width: 1.0 },
            'X': { openness: 0.0, width: 1.0 } // Rest position
        };

        const shape = mouthShapes[viseme] || mouthShapes['X'];
        
        // Apply visual feedback (simplified)
        this.drawMouthIndicator(shape, intensity);
    }

    drawMouthIndicator(shape, intensity) {
        // Draw a simple mouth indicator for demonstration
        // In the full implementation, this would be replaced with actual image warping
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height * 0.75; // Approximate mouth position
        
        this.ctx.save();
        this.ctx.globalAlpha = 0.3;
        this.ctx.fillStyle = '#ff6b6b';
        
        // Draw mouth shape indicator
        const width = 30 * shape.width * intensity;
        const height = 15 * shape.openness * intensity;
        
        this.ctx.beginPath();
        this.ctx.ellipse(centerX, centerY, width, height, 0, 0, 2 * Math.PI);
        this.ctx.fill();
        
        this.ctx.restore();
    }

    stopAnimation() {
        this.isAnimating = false;
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
        
        // Return to static avatar
        setTimeout(() => {
            this.drawStaticAvatar();
        }, 100);
    }

    // Utility method to create simple test animation
    createTestAnimation(duration = 3000) {
        const frames = [];
        const frameCount = Math.floor(duration / this.frameDuration);
        const visemes = ['A', 'E', 'I', 'O', 'U', 'B', 'M', 'X'];
        
        for (let i = 0; i < frameCount; i++) {
            const progress = i / frameCount;
            const visemeIndex = Math.floor(progress * visemes.length);
            const viseme = visemes[visemeIndex] || 'X';
            
            frames.push({
                viseme: viseme,
                intensity: Math.sin(progress * Math.PI * 4) * 0.5 + 0.5,
                timestamp: i * this.frameDuration
            });
        }
        
        return frames;
    }

    async processAvatarWithPython(imageFile) {
        try {
            // Send image to Python server for facial landmark processing
            const formData = new FormData();
            formData.append('image', imageFile);

            const response = await fetch('http://localhost:5001/process-avatar', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.facialLandmarks = result.landmarks;
                    this.currentAvatarFilename = result.filename;
                    console.log('Avatar processed with facial landmarks:', this.facialLandmarks);
                    console.log('Avatar filename stored:', this.currentAvatarFilename);
                } else {
                    console.warn('Python server failed to process avatar:', result.error);
                }
            } else {
                console.warn('Failed to communicate with Python server');
            }
        } catch (error) {
            console.error('Error processing avatar with Python server:', error);
            throw error;
        }
    }

    async generateLipSyncFrames(animationData, audioPath) {
        try {
            if (!this.facialLandmarks) {
                console.warn('No facial landmarks available, using enhanced simple animation');
                return this.enhanceAnimationData(animationData);
            }

            // Send animation request to Python server with correct parameters
            const response = await fetch('http://localhost:5001/generate-lip-sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    avatar_filename: this.currentAvatarFilename,
                    audio_path: audioPath,
                    rhubarb_data: animationData
                })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    console.log('Generated lip sync frames from Python server');
                    return result.animation_frames || result.frames;
                }
            }

            console.warn('Failed to generate lip sync frames, using enhanced simple animation');
            return this.enhanceAnimationData(animationData);
        } catch (error) {
            console.error('Error generating lip sync frames:', error);
            return this.enhanceAnimationData(animationData);
        }
    }

    enhanceAnimationData(animationData) {
        // Enhance the simple animation data with better viseme timing
        return animationData.map((frame, index) => ({
            ...frame,
            enhanced: true,
            frame_index: index,
            timestamp: frame.timestamp || (index * 33.33), // 30 FPS
            viseme: frame.viseme || 'X',
            intensity: Math.max(0.3, frame.intensity || 0.5) // Ensure minimum intensity
        }));
    }

    drawLipSyncFrame(frameData) {
        if (!this.avatarImage || !frameData) return;

        // Clear and redraw base image
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.drawImage(this.avatarImage, 0, 0, this.canvas.width, this.canvas.height);

        // Always draw enhanced viseme indicator for better visibility
        this.drawEnhancedVisemeIndicator(frameData);

        // Also apply frame transformations for additional visual feedback
        this.applyFrameTransformations(frameData);

        // Add prominent animation indicators
        this.drawLipSyncIndicators(frameData);

        // Add dramatic visual effects for lip sync
        this.drawDramaticLipSyncEffects(frameData);
    }

    drawEnhancedVisemeIndicator(frameData) {
        const viseme = frameData.viseme || 'X';
        const intensity = Math.max(0.4, frameData.intensity || 0.5); // Minimum intensity for visibility

        // Draw viseme-specific mouth shape overlay - make it much more prominent
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height * 0.72; // Slightly higher for better positioning

        this.ctx.save();
        this.ctx.globalAlpha = 0.9; // Much more opaque

        // Different colors for different viseme types - brighter colors
        const visemeColors = {
            'A': '#ff3333', 'E': '#33ff99', 'I': '#3399ff', 'O': '#ffcc33', 'U': '#ff9933',
            'B': '#ff1a1a', 'M': '#9933ff', 'F': '#ff33cc', 'V': '#ff6699',
            'X': '#888888' // Rest position
        };

        this.ctx.fillStyle = visemeColors[viseme] || visemeColors['X'];
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 3;

        // Draw viseme-specific shape with border
        this.drawVisemeShape(centerX, centerY, viseme, intensity);
        this.ctx.stroke();

        this.ctx.restore();
    }

    drawVisemeShape(centerX, centerY, viseme, intensity) {
        const baseSize = 40; // Much larger base size
        const size = baseSize * (0.7 + intensity * 0.8); // Larger range

        this.ctx.beginPath();

        switch(viseme) {
            case 'A':
                // Wide open mouth - very dramatic
                this.ctx.ellipse(centerX, centerY, size * 1.8, size * 1.2, 0, 0, 2 * Math.PI);
                break;
            case 'E':
                // Horizontal ellipse - wide smile
                this.ctx.ellipse(centerX, centerY, size * 2.2, size * 0.6, 0, 0, 2 * Math.PI);
                break;
            case 'I':
                // Small horizontal line - tight smile
                this.ctx.ellipse(centerX, centerY, size * 1.8, size * 0.3, 0, 0, 2 * Math.PI);
                break;
            case 'O':
                // Round shape - perfect circle
                this.ctx.ellipse(centerX, centerY, size * 1.0, size * 1.0, 0, 0, 2 * Math.PI);
                break;
            case 'U':
                // Small round shape - pucker
                this.ctx.ellipse(centerX, centerY, size * 0.8, size * 0.8, 0, 0, 2 * Math.PI);
                break;
            case 'B':
            case 'M':
                // Closed mouth - thin line
                this.ctx.ellipse(centerX, centerY, size * 1.5, size * 0.2, 0, 0, 2 * Math.PI);
                break;
            case 'F':
            case 'V':
                // Teeth on lip
                this.ctx.ellipse(centerX, centerY, size * 1.3, size * 0.4, 0, 0, 2 * Math.PI);
                break;
            default:
                // Rest position
                this.ctx.ellipse(centerX, centerY, size * 1.2, size * 0.5, 0, 0, 2 * Math.PI);
        }

        this.ctx.fill();
    }

    drawLipSyncIndicators(frameData) {
        // Add prominent indicators that lip sync is active
        const intensity = frameData.intensity || 0.5;
        const viseme = frameData.viseme || 'X';

        this.ctx.save();

        // Large corner indicator with pulsing effect
        this.ctx.fillStyle = `rgba(0, 255, 100, ${intensity})`;
        this.ctx.fillRect(this.canvas.width - 25, 5, 20, 20);

        // Viseme text - larger and more prominent
        this.ctx.fillStyle = `rgba(255, 255, 255, 1.0)`;
        this.ctx.font = 'bold 16px Arial';
        this.ctx.fillText(viseme, this.canvas.width - 35, 20);

        // Top banner with viseme info
        this.ctx.fillStyle = `rgba(0, 0, 0, 0.8)`;
        this.ctx.fillRect(0, 0, this.canvas.width, 35);

        this.ctx.fillStyle = `rgba(255, 255, 255, 1.0)`;
        this.ctx.font = 'bold 18px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`🗣️ LIP SYNC: ${viseme} (${Math.round(intensity * 100)}%)`, this.canvas.width / 2, 23);

        this.ctx.restore();
    }

    drawDramaticLipSyncEffects(frameData) {
        const intensity = frameData.intensity || 0.5;
        const viseme = frameData.viseme || 'X';

        this.ctx.save();

        // Pulsing border effect
        this.ctx.strokeStyle = `rgba(255, 100, 100, ${intensity})`;
        this.ctx.lineWidth = 8 * intensity;
        this.ctx.strokeRect(4, 4, this.canvas.width - 8, this.canvas.height - 8);

        // Sound wave visualization around the mouth area
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height * 0.72;

        for (let i = 0; i < 4; i++) {
            const radius = 60 + i * 25 + intensity * 20;
            this.ctx.beginPath();
            this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            this.ctx.strokeStyle = `rgba(100, 255, 100, ${(1 - i * 0.2) * intensity * 0.6})`;
            this.ctx.lineWidth = 4;
            this.ctx.stroke();
        }

        // Intensity bars on the sides
        const barHeight = this.canvas.height * 0.6;
        const barWidth = 8;
        const barIntensity = intensity * barHeight;

        // Left bar
        this.ctx.fillStyle = `rgba(255, 200, 0, ${intensity})`;
        this.ctx.fillRect(5, this.canvas.height - barIntensity - 20, barWidth, barIntensity);

        // Right bar
        this.ctx.fillRect(this.canvas.width - barWidth - 5, this.canvas.height - barIntensity - 20, barWidth, barIntensity);

        this.ctx.restore();
    }
}
