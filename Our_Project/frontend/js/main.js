// Main application controller
class TalkingAvatarApp {
    constructor() {
        this.apiClient = new ApiClient();
        this.audioRecorder = new AudioRecorder();
        this.avatarRenderer = new AvatarRenderer();
        
        this.currentAvatarFile = null;
        this.isProcessing = false;
        
        this.initializeEventListeners();
        this.checkSystemStatus();
    }

    initializeEventListeners() {
        // Avatar upload
        const uploadArea = document.getElementById('uploadArea');
        const avatarInput = document.getElementById('avatarInput');
        const changeAvatarBtn = document.getElementById('changeAvatar');

        uploadArea.addEventListener('click', () => avatarInput.click());
        avatarInput.addEventListener('change', (e) => this.handleAvatarUpload(e));
        changeAvatarBtn?.addEventListener('click', () => avatarInput.click());

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.processAvatarFile(files[0]);
            }
        });

        // Audio recording - use click for Web Speech API
        const recordBtn = document.getElementById('recordBtn');
        recordBtn.addEventListener('click', () => this.toggleRecording());

        // Text input
        const textInput = document.getElementById('textInput');
        const sendBtn = document.getElementById('sendBtn');
        
        sendBtn.addEventListener('click', () => this.handleTextInput());
        textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleTextInput();
            }
        });

        // Voice selection
        const voiceSelect = document.getElementById('voiceSelect');
        voiceSelect.addEventListener('change', (e) => {
            this.updateVoiceSelection(e.target.value);
        });

        // Debug button
        const debugBtn = document.getElementById('debugBtn');
        debugBtn.addEventListener('click', () => this.testSpeechAPI());
    }

    async handleAvatarUpload(event) {
        const file = event.target.files[0];
        if (file) {
            await this.processAvatarFile(file);
        }
    }

    async processAvatarFile(file) {
        try {
            this.updateStatus('avatarProcessingStatus', '🟡 Processing...');
            
            // Upload file to server
            const result = await this.apiClient.uploadAvatar(file);
            
            if (result.success) {
                this.currentAvatarFile = result.filename;
                
                // Show preview
                this.showAvatarPreview(file);
                
                // Initialize avatar renderer
                await this.avatarRenderer.loadAvatar(file);
                
                // Enable controls
                this.enableControls();
                
                this.updateStatus('avatarProcessingStatus', '🟢 Ready');
                this.updateAvatarStatus('Avatar loaded and ready!');
            }
        } catch (error) {
            console.error('Avatar upload error:', error);
            this.updateStatus('avatarProcessingStatus', '🔴 Error');
            this.updateAvatarStatus('Failed to process avatar image');
        }
    }

    showAvatarPreview(file) {
        const uploadArea = document.getElementById('uploadArea');
        const avatarPreview = document.getElementById('avatarPreview');
        const avatarImage = document.getElementById('avatarImage');

        const reader = new FileReader();
        reader.onload = (e) => {
            avatarImage.src = e.target.result;
            uploadArea.style.display = 'none';
            avatarPreview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }

    enableControls() {
        document.getElementById('recordBtn').disabled = false;
        document.getElementById('textInput').disabled = false;
        document.getElementById('sendBtn').disabled = false;
    }

    toggleRecording() {
        if (this.isListening) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    async startRecording() {
        if (this.isProcessing || this.isListening) return;

        try {
            this.isListening = true;
            this.updateRecordingStatus('🔴 Listening... (click to stop)');
            document.getElementById('recordBtn').style.background = '#e53e3e';
            document.getElementById('recordBtn').innerHTML = '<span class="record-icon">⏹️</span><span class="record-text">Stop Listening</span>';

            // Start Web Speech Recognition
            this.startWebSpeechRecognition();

        } catch (error) {
            console.error('Recording start error:', error);
            this.updateRecordingStatus('❌ Failed to start recording');
            this.isListening = false;
        }
    }

    async stopRecording() {
        if (!this.isListening) return;

        // Stop Web Speech Recognition
        this.stopWebSpeechRecognition();

        this.isListening = false;
        this.updateRecordingStatus('');
        document.getElementById('recordBtn').style.background = '#667eea';
        document.getElementById('recordBtn').innerHTML = '<span class="record-icon">🎤</span><span class="record-text">Start Listening</span>';
    }

    startWebSpeechRecognition() {
        console.log('Attempting to start Web Speech Recognition...');

        // Check if Web Speech API is supported
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        if (!SpeechRecognition) {
            console.error('Web Speech API not supported');
            this.updateRecordingStatus('❌ Speech recognition not supported in this browser');
            this.stopRecording();
            return;
        }

        try {
            this.recognition = new SpeechRecognition();
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'en-US';
            this.recognition.maxAlternatives = 1;

            console.log('Speech recognition configured');

            this.recognition.onstart = () => {
                console.log('Speech recognition started');
                this.updateRecordingStatus('🔴 Listening... (speak now)');
            };

            this.recognition.onresult = (event) => {
                console.log('Speech recognition result received:', event);
                const transcript = event.results[0][0].transcript;
                const confidence = event.results[0][0].confidence;
                console.log('Transcript:', transcript, 'Confidence:', confidence);

                // Stop listening after getting result
                this.stopRecording();

                if (transcript && transcript.trim()) {
                    this.addMessage('user', transcript);
                    this.generateAIResponse(transcript);
                } else {
                    this.updateRecordingStatus('❌ No speech detected');
                }
            };

            this.recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error, event);
                this.stopRecording();

                let errorMessage = 'Speech recognition failed';
                switch(event.error) {
                    case 'no-speech':
                        errorMessage = 'No speech detected. Try speaking louder.';
                        break;
                    case 'audio-capture':
                        errorMessage = 'Microphone not accessible. Check permissions.';
                        break;
                    case 'not-allowed':
                        errorMessage = 'Microphone permission denied. Please allow microphone access.';
                        break;
                    case 'network':
                        errorMessage = 'Network error. Web Speech API requires internet connection. Try installing SuperWhisper for offline speech recognition.';
                        break;
                    default:
                        errorMessage = `Speech error: ${event.error}`;
                }

                this.updateRecordingStatus(`❌ ${errorMessage}`);
                this.addMessage('system', errorMessage);
            };

            this.recognition.onend = () => {
                console.log('Speech recognition ended');
                if (this.isListening) {
                    this.stopRecording();
                }
            };

            console.log('Starting speech recognition...');
            this.recognition.start();

        } catch (error) {
            console.error('Failed to initialize speech recognition:', error);
            this.updateRecordingStatus('❌ Failed to start speech recognition');
            this.stopRecording();
        }
    }

    stopWebSpeechRecognition() {
        if (this.recognition) {
            this.recognition.stop();
            this.recognition = null;
        }
    }

    async handleTextInput() {
        const textInput = document.getElementById('textInput');
        const message = textInput.value.trim();
        
        if (!message || this.isProcessing) return;
        
        textInput.value = '';
        await this.processTextInput(message);
    }

    async processAudioInput(audioBlob) {
        try {
            this.isProcessing = true;
            this.updateRecordingStatus('🟡 Converting speech to text...');

            // Try Web Speech API first (faster and more reliable)
            try {
                this.updateRecordingStatus('🟡 Using browser speech recognition...');
                const transcript = await this.audioRecorder.tryWebSpeechRecognition();

                if (transcript && transcript.trim()) {
                    this.addMessage('user', transcript);
                    await this.generateAIResponse(transcript);
                    return;
                }
            } catch (webSpeechError) {
                console.log('Web Speech API failed, trying server-side:', webSpeechError.message);
            }

            // Fallback to server-side processing
            this.updateRecordingStatus('🟡 Using server speech recognition...');
            const transcription = await this.apiClient.speechToText(audioBlob);

            if (transcription.success && transcription.transcription.trim()) {
                this.addMessage('user', transcription.transcription);
                await this.generateAIResponse(transcription.transcription);
            } else {
                this.addMessage('system', 'Could not understand speech. Please try speaking more clearly or use text input.');
            }
        } catch (error) {
            console.error('Audio processing error:', error);
            this.updateRecordingStatus('❌ Failed to process audio');
            this.addMessage('system', 'Speech recognition failed. Please try typing your message.');
        } finally {
            this.isProcessing = false;
            this.updateRecordingStatus('');
        }
    }

    async processTextInput(message) {
        try {
            this.isProcessing = true;
            this.addMessage('user', message);
            await this.generateAIResponse(message);
        } catch (error) {
            console.error('Text processing error:', error);
        } finally {
            this.isProcessing = false;
        }
    }

    async generateAIResponse(message) {
        try {
            this.updateStatus('ollamaStatus', '🟡 Thinking...');
            
            // Generate AI response
            const response = await this.apiClient.generateResponse(message);
            
            if (response.success) {
                this.addMessage('ai', response.response);
                
                // Generate speech and animation
                await this.generateSpeechAndAnimation(response.response);
            }
            
            this.updateStatus('ollamaStatus', '🟢 Ready');
        } catch (error) {
            console.error('AI response error:', error);
            this.updateStatus('ollamaStatus', '🔴 Error');
            this.addMessage('ai', 'Sorry, I encountered an error processing your request.');
        }
    }

    async generateSpeechAndAnimation(text) {
        try {
            this.updateAvatarStatus('Generating speech and animation...');
            
            const result = await this.apiClient.generateSpeech(text, this.currentAvatarFile);
            
            if (result.success) {
                // Play audio and animate avatar
                await this.avatarRenderer.playAnimation(result.animationData, result.audioPath);
                this.updateAvatarStatus('Speaking...');
            }
        } catch (error) {
            console.error('Speech generation error:', error);
            this.updateAvatarStatus('Failed to generate speech');
        }
    }

    addMessage(sender, text) {
        const history = document.getElementById('conversationHistory');
        const placeholder = history.querySelector('.message-placeholder');
        
        if (placeholder) {
            placeholder.remove();
        }
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        messageDiv.textContent = text;
        
        history.appendChild(messageDiv);
        history.scrollTop = history.scrollHeight;
    }

    updateStatus(elementId, status) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = status;
        }
    }

    async updateVoiceSelection(voice) {
        try {
            // Send voice selection to backend
            const response = await fetch(`http://localhost:3001/api/update-voice`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ voice })
            });

            if (response.ok) {
                console.log('Voice updated to:', voice);
                // Show confirmation
                this.addMessage('system', `Voice changed to ${voice}`);
            } else {
                console.error('Failed to update voice');
            }
        } catch (error) {
            console.error('Failed to update voice:', error);
        }
    }

    updateRecordingStatus(status) {
        document.getElementById('recordingStatus').textContent = status;
    }

    updateAvatarStatus(status) {
        document.getElementById('avatarStatus').textContent = status;
    }

    testSpeechAPI() {
        console.log('Testing Speech API...');

        // Check browser support
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        if (!SpeechRecognition) {
            alert('❌ Web Speech API not supported in this browser.\n\nTry using Chrome, Edge, or Safari.');
            return;
        }

        // Check internet connectivity
        if (!navigator.onLine) {
            alert('❌ No internet connection detected.\n\nWeb Speech API requires internet access.\nConsider installing SuperWhisper for offline speech recognition.');
            return;
        }

        // Check microphone permissions
        navigator.mediaDevices.getUserMedia({ audio: true })
            .then(() => {
                alert('✅ Speech API supported!\n✅ Microphone access granted!\n✅ Internet connection detected!\n\nTry clicking "Start Listening" now.\n\nNote: If you still get network errors, your firewall might be blocking Google\'s speech servers.');
            })
            .catch((error) => {
                alert(`❌ Microphone access denied or not available.\n\nError: ${error.message}\n\nPlease allow microphone access and try again.`);
            });
    }

    async checkSystemStatus() {
        // Check Ollama status
        try {
            const response = await fetch('http://localhost:11434/api/tags');
            if (response.ok) {
                this.updateStatus('ollamaStatus', '🟢 Connected');
            } else {
                this.updateStatus('ollamaStatus', '🔴 Not running');
            }
        } catch (error) {
            this.updateStatus('ollamaStatus', '🔴 Not available');
        }

        // Check speech recognition
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        if (SpeechRecognition) {
            this.updateStatus('speechStatus', '🟢 Available');
        } else {
            this.updateStatus('speechStatus', '🔴 Not supported');
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TalkingAvatarApp();
});
