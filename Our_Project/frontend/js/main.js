// Main application controller
class TalkingAvatarApp {
    constructor() {
        this.apiClient = new ApiClient();
        this.audioRecorder = new AudioRecorder();
        this.avatarRenderer = new AvatarRenderer();

        this.currentAvatarFile = null;
        this.isProcessing = false;

        this.initializeEventListeners();
        this.checkSystemStatus();
        this.loadSavedAvatar();

        // Ensure button state is correct every few seconds
        setInterval(() => this.ensureCorrectButtonState(), 2000);
    }

    initializeEventListeners() {
        // Avatar upload
        const uploadArea = document.getElementById('uploadArea');
        const avatarInput = document.getElementById('avatarInput');
        const changeAvatarBtn = document.getElementById('changeAvatar');
        const clearSavedAvatarBtn = document.getElementById('clearSavedAvatar');

        uploadArea.addEventListener('click', () => avatarInput.click());
        avatarInput.addEventListener('change', (e) => this.handleAvatarUpload(e));
        changeAvatarBtn?.addEventListener('click', () => avatarInput.click());
        clearSavedAvatarBtn?.addEventListener('click', () => this.handleClearSavedAvatar());

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.processAvatarFile(files[0]);
            }
        });

        // Audio recording - use click for Web Speech API
        const recordBtn = document.getElementById('recordBtn');
        recordBtn.addEventListener('click', () => this.toggleRecording());

        // Text input
        const textInput = document.getElementById('textInput');
        const sendBtn = document.getElementById('sendBtn');
        
        sendBtn.addEventListener('click', () => this.handleTextInput());
        textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleTextInput();
            }
        });

        // Voice selection
        const voiceSelect = document.getElementById('voiceSelect');
        voiceSelect.addEventListener('change', (e) => {
            this.updateVoiceSelection(e.target.value);
        });

        // Debug button
        const debugBtn = document.getElementById('debugBtn');
        debugBtn.addEventListener('click', () => this.testSpeechAPI());
    }

    async handleAvatarUpload(event) {
        const file = event.target.files[0];
        if (file) {
            await this.processAvatarFile(file);
        }
    }

    async processAvatarFile(file) {
        try {
            this.updateStatus('avatarProcessingStatus', '🟡 Processing...');

            // Upload file to server
            const result = await this.apiClient.uploadAvatar(file);

            if (result.success) {
                this.currentAvatarFile = result.filename;

                // Show preview
                this.showAvatarPreview(file);

                // Initialize avatar renderer
                await this.avatarRenderer.loadAvatar(file);

                // Save avatar to localStorage
                this.saveAvatarToStorage(file);

                // Enable controls
                this.enableControls();

                this.updateStatus('avatarProcessingStatus', '🟢 Ready');
                this.updateAvatarStatus('Avatar loaded and ready!');
            }
        } catch (error) {
            console.error('Avatar upload error:', error);
            this.updateStatus('avatarProcessingStatus', '🔴 Error');
            this.updateAvatarStatus('Failed to process avatar image');
        }
    }

    showAvatarPreview(file) {
        const uploadArea = document.getElementById('uploadArea');
        const avatarPreview = document.getElementById('avatarPreview');
        const avatarImage = document.getElementById('avatarImage');

        const reader = new FileReader();
        reader.onload = (e) => {
            avatarImage.src = e.target.result;
            uploadArea.style.display = 'none';
            avatarPreview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }

    enableControls() {
        document.getElementById('recordBtn').disabled = false;
        document.getElementById('textInput').disabled = false;
        document.getElementById('sendBtn').disabled = false;
    }

    toggleRecording() {
        if (this.isListening) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    async startRecording() {
        if (this.isProcessing || this.isListening) return;

        try {
            this.isListening = true;
            this.updateRecordingStatus('🔴 Recording... (click to stop)');
            document.getElementById('recordBtn').style.background = '#e53e3e';
            document.getElementById('recordBtn').innerHTML = '<span class="record-icon">⏹️</span><span class="record-text">Stop Recording</span>';

            // Start local audio recording
            await this.audioRecorder.startRecording();

        } catch (error) {
            console.error('Recording start error:', error);
            this.updateRecordingStatus('❌ Failed to start recording');
            this.isListening = false;
            this.resetRecordButton();
        }
    }

    async stopRecording() {
        if (!this.isListening) return;

        // Immediately reset button state to prevent UI issues
        this.isListening = false;
        this.resetRecordButton();

        try {
            this.updateRecordingStatus('🟡 Processing audio...');

            // Stop recording and get audio blob
            const audioBlob = await this.audioRecorder.stopRecording();

            // Process the recorded audio
            await this.processAudioInput(audioBlob);

        } catch (error) {
            console.error('Recording stop error:', error);
            this.updateRecordingStatus('❌ Failed to process recording');
            // Clear error status after delay
            setTimeout(() => {
                this.updateRecordingStatus('');
            }, 3000);
        }
    }

    resetRecordButton() {
        const recordBtn = document.getElementById('recordBtn');
        if (recordBtn) {
            recordBtn.style.background = '#667eea';
            recordBtn.innerHTML = '<span class="record-icon">🎤</span><span class="record-text">Start Recording</span>';
        }
    }

    ensureCorrectButtonState() {
        // Ensure button state matches internal state
        if (this.isListening) {
            const recordBtn = document.getElementById('recordBtn');
            if (recordBtn) {
                recordBtn.style.background = '#e53e3e';
                recordBtn.innerHTML = '<span class="record-icon">⏹️</span><span class="record-text">Stop Recording</span>';
            }
        } else {
            this.resetRecordButton();
        }
    }



    async handleTextInput() {
        const textInput = document.getElementById('textInput');
        const message = textInput.value.trim();
        
        if (!message || this.isProcessing) return;
        
        textInput.value = '';
        await this.processTextInput(message);
    }

    async processAudioInput(audioBlob) {
        try {
            this.isProcessing = true;
            this.updateRecordingStatus('🟡 Converting speech to text...');

            // Try live Web Speech API first (works directly in browser)
            if (this.shouldTryWebSpeechAPI()) {
                this.updateRecordingStatus('🟡 Trying browser speech recognition...');
                const webSpeechResult = await this.tryLiveWebSpeechAPI();
                if (webSpeechResult.success) {
                    const text = webSpeechResult.transcription.trim();
                    this.addMessage('user', text);
                    this.updateRecordingStatus('✅ Speech recognized (Browser)');
                    await this.generateAIResponse(text);
                    return;
                }
            }

            // Fallback to server-side speech recognition
            this.updateRecordingStatus('🟡 Processing with local speech recognition...');
            const transcription = await this.apiClient.speechToText(audioBlob);

            if (transcription.success && transcription.transcription.trim()) {
                // Check if this is actually a transcription or an error message
                const text = transcription.transcription.trim();

                // If it contains error-like phrases, treat as system message
                if (text.includes('detected your voice but couldn\'t transcribe') ||
                    text.includes('SuperWhisper') ||
                    text.includes('install') ||
                    text.includes('speech recognition')) {
                    this.addMessage('system', text);
                    this.updateRecordingStatus('❌ Speech recognition not available');
                } else {
                    // Valid transcription - process as user input
                    this.addMessage('user', text);
                    await this.generateAIResponse(text);
                }
            } else {
                // No transcription received
                this.addMessage('system', 'Could not understand speech. Please try speaking more clearly or use text input.');
                this.updateRecordingStatus('❌ No speech detected');
            }
        } catch (error) {
            console.error('Audio processing error:', error);
            this.updateRecordingStatus('❌ Failed to process audio');
            this.addMessage('system', 'Speech recognition failed. For better speech recognition, please install SuperWhisper from https://superwhisper.com/ or try typing your message.');
        } finally {
            this.isProcessing = false;
            // Clear status after a delay
            setTimeout(() => {
                this.updateRecordingStatus('');
            }, 3000);
        }
    }

    async processTextInput(message) {
        try {
            this.isProcessing = true;
            this.addMessage('user', message);
            await this.generateAIResponse(message);
        } catch (error) {
            console.error('Text processing error:', error);
        } finally {
            this.isProcessing = false;
        }
    }

    shouldTryWebSpeechAPI() {
        // Only try Web Speech API if it's supported and we don't have SuperWhisper
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        return SpeechRecognition && (!this.speechRecognitionStatus || !this.speechRecognitionStatus.available.includes('superwhisper'));
    }

    async tryLiveWebSpeechAPI() {
        return new Promise((resolve) => {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            if (!SpeechRecognition) {
                resolve({ success: false, error: 'Web Speech API not supported' });
                return;
            }

            try {
                const recognition = new SpeechRecognition();
                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'en-US';

                let hasResult = false;

                recognition.onresult = (event) => {
                    hasResult = true;
                    const transcript = event.results[0][0].transcript;
                    console.log('Web Speech API result:', transcript);
                    resolve({ success: true, transcription: transcript });
                };

                recognition.onerror = (event) => {
                    console.warn('Web Speech API error:', event.error);
                    if (!hasResult) {
                        resolve({ success: false, error: event.error });
                    }
                };

                recognition.onend = () => {
                    if (!hasResult) {
                        resolve({ success: false, error: 'No speech detected' });
                    }
                };

                // Start recognition with live microphone
                recognition.start();

                // Set timeout
                setTimeout(() => {
                    if (!hasResult) {
                        recognition.stop();
                        resolve({ success: false, error: 'Timeout' });
                    }
                }, 8000); // 8 second timeout for live speech

            } catch (error) {
                console.warn('Web Speech API setup error:', error);
                resolve({ success: false, error: error.message });
            }
        });
    }

    async generateAIResponse(message) {
        try {
            this.updateStatus('ollamaStatus', '🟡 Thinking...');
            
            // Generate AI response
            const response = await this.apiClient.generateResponse(message);
            
            if (response.success) {
                this.addMessage('ai', response.response);
                
                // Generate speech and animation
                await this.generateSpeechAndAnimation(response.response);
            }
            
            this.updateStatus('ollamaStatus', '🟢 Ready');
        } catch (error) {
            console.error('AI response error:', error);
            this.updateStatus('ollamaStatus', '🔴 Error');
            this.addMessage('ai', 'Sorry, I encountered an error processing your request.');
        }
    }

    async generateSpeechAndAnimation(text) {
        try {
            this.updateAvatarStatus('Generating speech and animation...');
            
            const result = await this.apiClient.generateSpeech(text, this.currentAvatarFile);
            
            if (result.success) {
                // Play audio and animate avatar
                await this.avatarRenderer.playAnimation(result.animationData, result.audioPath);
                this.updateAvatarStatus('Speaking...');
            }
        } catch (error) {
            console.error('Speech generation error:', error);
            this.updateAvatarStatus('Failed to generate speech');
        }
    }

    addMessage(sender, text) {
        const history = document.getElementById('conversationHistory');
        const placeholder = history.querySelector('.message-placeholder');
        
        if (placeholder) {
            placeholder.remove();
        }
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        messageDiv.textContent = text;
        
        history.appendChild(messageDiv);
        history.scrollTop = history.scrollHeight;
    }

    updateStatus(elementId, status) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = status;
        }
    }

    async updateVoiceSelection(voice) {
        try {
            // Send voice selection to backend
            const response = await fetch(`http://localhost:3001/api/update-voice`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ voice })
            });

            if (response.ok) {
                console.log('Voice updated to:', voice);
                // Show confirmation
                this.addMessage('system', `Voice changed to ${voice}`);
            } else {
                console.error('Failed to update voice');
            }
        } catch (error) {
            console.error('Failed to update voice:', error);
        }
    }

    updateRecordingStatus(status) {
        document.getElementById('recordingStatus').textContent = status;
    }

    updateAvatarStatus(status) {
        document.getElementById('avatarStatus').textContent = status;
    }

    testSpeechAPI() {
        console.log('Testing Local Speech Recognition...');

        // Check microphone permissions and recording support
        if (!AudioRecorder.isSupported()) {
            alert('❌ Audio recording not supported in this browser.\n\nTry using Chrome, Edge, or Safari.');
            return;
        }

        // Check microphone permissions
        navigator.mediaDevices.getUserMedia({ audio: true })
            .then(() => {
                alert('✅ Microphone access granted!\n✅ Audio recording supported!\n\nLocal speech recognition ready.\n\nNote: For best results, install SuperWhisper from https://superwhisper.com/\nOtherwise, the system will provide helpful guidance when you try to speak.');
            })
            .catch((error) => {
                alert(`❌ Microphone access denied or not available.\n\nError: ${error.message}\n\nPlease allow microphone access and try again.`);
            });
    }

    async checkSystemStatus() {
        // Check Ollama status
        try {
            const response = await fetch('http://localhost:11434/api/tags');
            if (response.ok) {
                this.updateStatus('ollamaStatus', '🟢 Connected');
            } else {
                this.updateStatus('ollamaStatus', '🔴 Not running');
            }
        } catch (error) {
            this.updateStatus('ollamaStatus', '🔴 Not available');
        }

        // Check speech recognition status
        await this.checkSpeechRecognitionStatus();
    }

    async checkSpeechRecognitionStatus() {
        try {
            // Check if audio recording is supported
            if (!AudioRecorder.isSupported()) {
                this.updateStatus('speechStatus', '� Recording Not Supported');
                return;
            }

            // Check what speech recognition software is available
            const response = await fetch('http://localhost:3001/api/speech-status');
            if (response.ok) {
                const status = await response.json();

                if (status.hasAny) {
                    const availableNames = status.available.map(opt => {
                        switch(opt) {
                            case 'superwhisper': return 'SuperWhisper';
                            case 'whisper-cli': return 'Whisper CLI';
                            case 'macwhisper': return 'MacWhisper';
                            default: return opt;
                        }
                    }).join(', ');
                    this.updateStatus('speechStatus', `🟢 Available: ${availableNames}`);
                } else {
                    this.updateStatus('speechStatus', '� Recording Only (No STT)');
                }

                // Store status for later use
                this.speechRecognitionStatus = status;
            } else {
                this.updateStatus('speechStatus', '🟢 Local Recording Ready');
            }
        } catch (error) {
            console.error('Failed to check speech recognition status:', error);
            this.updateStatus('speechStatus', '🟢 Local Recording Ready');
        }
    }

    // Avatar persistence methods
    saveAvatarToStorage(file) {
        try {
            const reader = new FileReader();
            reader.onload = (e) => {
                const avatarData = {
                    dataUrl: e.target.result,
                    filename: this.currentAvatarFile,
                    timestamp: Date.now()
                };
                localStorage.setItem('savedAvatar', JSON.stringify(avatarData));
                console.log('Avatar saved to localStorage');
            };
            reader.readAsDataURL(file);
        } catch (error) {
            console.error('Failed to save avatar to storage:', error);
        }
    }

    async loadSavedAvatar() {
        try {
            const savedAvatarData = localStorage.getItem('savedAvatar');
            if (!savedAvatarData) return;

            const avatarData = JSON.parse(savedAvatarData);

            // Convert data URL back to file
            const response = await fetch(avatarData.dataUrl);
            const blob = await response.blob();
            const file = new File([blob], 'saved-avatar.jpg', { type: blob.type });

            // Set current avatar file
            this.currentAvatarFile = avatarData.filename;

            // Show preview
            this.showAvatarPreviewFromDataUrl(avatarData.dataUrl);

            // Initialize avatar renderer
            await this.avatarRenderer.loadAvatar(file);

            // Enable controls
            this.enableControls();

            this.updateStatus('avatarProcessingStatus', '🟢 Ready');
            this.updateAvatarStatus('Saved avatar loaded and ready!');

            console.log('Saved avatar loaded successfully');
        } catch (error) {
            console.error('Failed to load saved avatar:', error);
            // Clear invalid saved data
            localStorage.removeItem('savedAvatar');
        }
    }

    showAvatarPreviewFromDataUrl(dataUrl) {
        const uploadArea = document.getElementById('uploadArea');
        const avatarPreview = document.getElementById('avatarPreview');
        const avatarImage = document.getElementById('avatarImage');

        avatarImage.src = dataUrl;
        uploadArea.style.display = 'none';
        avatarPreview.style.display = 'block';
    }

    clearSavedAvatar() {
        localStorage.removeItem('savedAvatar');
        console.log('Saved avatar cleared');
    }

    handleClearSavedAvatar() {
        if (confirm('Are you sure you want to clear the saved avatar? You will need to upload a new image.')) {
            this.clearSavedAvatar();

            // Reset UI to upload state
            const uploadArea = document.getElementById('uploadArea');
            const avatarPreview = document.getElementById('avatarPreview');

            uploadArea.style.display = 'block';
            avatarPreview.style.display = 'none';

            // Clear current avatar
            this.currentAvatarFile = null;

            // Clear canvas
            const canvas = document.getElementById('avatarCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Disable controls
            document.getElementById('recordBtn').disabled = true;
            document.getElementById('textInput').disabled = true;
            document.getElementById('sendBtn').disabled = true;

            this.updateStatus('avatarProcessingStatus', '⚪ Waiting');
            this.updateAvatarStatus('Upload an image to get started');

            this.addMessage('system', 'Saved avatar cleared. Please upload a new image.');
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TalkingAvatarApp();
});
