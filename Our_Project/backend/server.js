const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const axios = require('axios');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../frontend')));

// File upload configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../assets/uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    cb(null, `avatar-${Date.now()}${path.extname(file.originalname)}`);
  }
});

const upload = multer({ storage });

// Routes

// Serve frontend
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../frontend/index.html'));
});

// Upload avatar image
app.post('/api/upload-avatar', upload.single('avatar'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No file uploaded' });
  }

  res.json({
    success: true,
    filename: req.file.filename,
    path: req.file.path
  });
});

// Process speech-to-text
app.post('/api/speech-to-text', upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No audio file uploaded' });
    }

    // Call SuperWhisper or MacWhisper
    const transcription = await processAudioWithWhisper(req.file.path);

    res.json({
      success: true,
      transcription: transcription
    });
  } catch (error) {
    console.error('Speech-to-text error:', error);
    res.status(500).json({ error: 'Failed to process audio' });
  }
});

// Generate AI response
app.post('/api/generate-response', async (req, res) => {
  try {
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'No message provided' });
    }

    // Call Ollama
    const response = await generateWithOllama(message);

    res.json({
      success: true,
      response: response.displayText || response,
      speechText: response.speechText || response
    });
  } catch (error) {
    console.error('AI response error:', error);
    res.status(500).json({ error: 'Failed to generate response' });
  }
});

// Generate speech and lip sync
app.post('/api/generate-speech', async (req, res) => {
  try {
    const { text, avatarFilename } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'No text provided' });
    }

    // Remove emojis from text for speech
    const speechText = text.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '').trim();

    // Generate speech with macOS TTS using clean text
    const audioPath = await generateSpeechWithSay(speechText);

    // Create simple animation data without complex processing
    const simpleAnimationData = createSimpleAnimation(speechText.length);

    // Extract just the filename for the frontend
    const audioFilename = path.basename(audioPath);

    console.log('Generated audio file:', audioFilename);
    console.log('Speech text (no emojis):', speechText);

    res.json({
      success: true,
      audioPath: audioFilename, // Just the filename
      lipSyncData: [],
      animationData: simpleAnimationData
    });
  } catch (error) {
    console.error('Speech generation error:', error);
    res.status(500).json({ error: 'Failed to generate speech' });
  }
});

// Update voice selection
app.post('/api/update-voice', (req, res) => {
  try {
    const { voice } = req.body;

    if (!voice) {
      return res.status(400).json({ error: 'No voice specified' });
    }

    // Update config file
    const configPath = path.join(__dirname, '../config.json');
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    config.speech.voice = voice;
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

    console.log('Voice updated to:', voice);
    res.json({ success: true, voice });
  } catch (error) {
    console.error('Voice update error:', error);
    res.status(500).json({ error: 'Failed to update voice' });
  }
});

// Check speech recognition status
app.get('/api/speech-status', (req, res) => {
  const availableOptions = checkAvailableSpeechRecognition();

  res.json({
    available: availableOptions,
    hasAny: availableOptions.length > 0,
    recommendations: {
      superwhisper: {
        name: 'SuperWhisper',
        url: 'https://superwhisper.com/',
        description: 'Best for macOS, optimized for Apple Silicon',
        available: availableOptions.includes('superwhisper')
      },
      whisper: {
        name: 'OpenAI Whisper CLI',
        install: 'pip install openai-whisper && brew install ffmpeg',
        description: 'Open source, works well with Python',
        available: availableOptions.includes('whisper-cli')
      },
      macwhisper: {
        name: 'MacWhisper',
        url: 'Mac App Store',
        description: 'Native macOS app',
        available: availableOptions.includes('macwhisper')
      }
    }
  });
});

// Serve audio files
app.get('/audio/:filename', (req, res) => {
  const filename = req.params.filename;
  const audioPath = path.join(__dirname, '../assets/audio', filename);

  console.log('Audio request for:', filename);
  console.log('Looking for file at:', audioPath);
  console.log('File exists:', fs.existsSync(audioPath));

  if (fs.existsSync(audioPath)) {
    res.sendFile(audioPath);
  } else {
    res.status(404).json({ error: 'Audio file not found' });
  }
});

// Helper functions

function createSimpleAnimation(textLength) {
  // Create a simple animation based on text length
  const duration = Math.max(textLength * 100, 2000); // Rough estimate: 100ms per character, min 2 seconds
  const frameCount = Math.ceil(duration / 33.33); // 30 FPS
  const frames = [];

  const visemes = ['A', 'E', 'I', 'O', 'U', 'B', 'M', 'X'];

  for (let i = 0; i < frameCount; i++) {
    const progress = i / frameCount;
    const visemeIndex = Math.floor(Math.sin(progress * Math.PI * 8) * 3 + 3); // Oscillate between visemes
    const viseme = visemes[visemeIndex] || 'X';

    frames.push({
      frame_index: i,
      viseme: viseme,
      timestamp: i * 33.33,
      intensity: Math.sin(progress * Math.PI * 4) * 0.5 + 0.5
    });
  }

  return frames;
}

async function processAudioWithWhisper(audioPath) {
  try {
    // Load configuration
    const config = JSON.parse(fs.readFileSync(path.join(__dirname, '../config.json'), 'utf8'));
    const whisperConfig = config.whisper;

    // Check what speech recognition options are available
    const availableOptions = checkAvailableSpeechRecognition();

    if (availableOptions.length === 0) {
      console.warn('No speech recognition methods available');
      return generateHelpfulSpeechMessage();
    }

    // Use SuperWhisper only (simplified approach)
    if (availableOptions.includes('superwhisper')) {
      try {
        return await processWithSuperWhisper(audioPath);
      } catch (error) {
        console.warn('SuperWhisper failed:', error.message);
        return `🎤 Audio recorded successfully!

SuperWhisper is installed but needs manual operation:

💡 **To transcribe with SuperWhisper:**
1. SuperWhisper should have opened automatically
2. Import the audio file or use the microphone
3. Copy the transcription and paste it here

**Or simply type your message below!** 👇`;
      }
    }

    // If all methods fail, provide helpful guidance
    console.warn('All available speech recognition methods failed');
    return generateHelpfulSpeechMessage();

  } catch (error) {
    console.error('Speech-to-text error:', error);
    throw new Error('Failed to process audio');
  }
}

function checkAvailableSpeechRecognition() {
  const available = [];

  // Check SuperWhisper
  const superWhisperPath = '/Applications/SuperWhisper.app/Contents/MacOS/SuperWhisper';
  if (fs.existsSync(superWhisperPath)) {
    available.push('superwhisper');
  }

  // Check MacWhisper (installed app)
  const macWhisperPath = '/Applications/MacWhisper.app/Contents/MacOS/MacWhisper';
  if (fs.existsSync(macWhisperPath)) {
    available.push('macwhisper');
  }

  // Check for OpenAI Whisper CLI in common locations
  const whisperPaths = [
    '/Users/<USER>/Library/Python/3.9/bin/whisper',
    '/usr/local/bin/whisper',
    '/opt/homebrew/bin/whisper'
  ];

  for (const whisperPath of whisperPaths) {
    if (fs.existsSync(whisperPath)) {
      available.push('whisper-cli');
      break;
    }
  }

  // Also try which command as fallback
  if (!available.includes('whisper-cli')) {
    try {
      const { execSync } = require('child_process');
      execSync('which whisper', { stdio: 'ignore' });
      available.push('whisper-cli');
    } catch (error) {
      // whisper command not found in PATH
    }
  }

  return available;
}

function generateHelpfulSpeechMessage() {
  const availableOptions = checkAvailableSpeechRecognition();

  if (availableOptions.length === 0) {
    return `🎤 Audio recorded successfully, but no speech recognition software is installed.

For local speech recognition, you can install:

1. 🌟 SuperWhisper (Recommended): https://superwhisper.com/
   - Best for macOS, optimized for Apple Silicon
   - Download and install from the website

2. 🔧 OpenAI Whisper CLI:
   - Run: pip install openai-whisper
   - Then: brew install ffmpeg

3. 📱 MacWhisper: Available on Mac App Store

Once installed, restart the application and try recording again.

For now, please type your message below! 👇`;
  } else {
    return `🎤 Audio recorded successfully!

Available speech recognition: ${availableOptions.join(', ')}

The transcription failed this time. This could be because:
• The audio was too quiet or unclear
• Background noise interfered
• The speech was too fast or unclear

💡 Tips for better recognition:
• Speak clearly and at normal pace
• Reduce background noise
• Make sure microphone permissions are granted

Please try recording again or type your message below! 👇`;
  }
}

async function processWithSuperWhisper(audioPath) {
  return new Promise((resolve, reject) => {
    const superWhisperPath = '/Applications/SuperWhisper.app/Contents/MacOS/SuperWhisper';

    if (!fs.existsSync(superWhisperPath)) {
      reject(new Error('SuperWhisper application not found'));
      return;
    }

    console.log('Processing with SuperWhisper Deep Links...');

    const { exec } = require('child_process');
    const absoluteAudioPath = path.resolve(audioPath);

    // Use SuperWhisper Deep Links for seamless integration
    // First, try to use a Voice mode (most common for transcription)
    const deepLinkCommand = `open "superwhisper://mode?key=voice" && sleep 1 && open "superwhisper://record"`;

    console.log('Executing SuperWhisper Deep Link:', deepLinkCommand);

    exec(deepLinkCommand, (deepLinkError) => {
      if (deepLinkError) {
        console.warn('SuperWhisper Deep Link failed, trying fallback:', deepLinkError.message);

        // Fallback: Just open SuperWhisper normally
        exec('open -a SuperWhisper', (openError) => {
          if (openError) {
            reject(new Error(`Failed to open SuperWhisper: ${openError.message}`));
            return;
          }

          // Copy file path for manual import
          exec(`echo "${absoluteAudioPath}" | pbcopy`, () => {
            resolve(`🎤 SuperWhisper opened (fallback mode)!

📁 Audio file: ${path.basename(audioPath)}
📋 File path copied to clipboard

**To transcribe:**
1. Import the audio file in SuperWhisper
2. Copy the transcription and paste it here

**Or simply type your message below!** 👇`);
          });
        });
      } else {
        console.log('SuperWhisper Deep Link executed successfully');

        // With Deep Links, SuperWhisper should be ready to record
        // We still need to handle the audio file, so copy it to clipboard
        exec(`echo "${absoluteAudioPath}" | pbcopy`, () => {
          resolve(`🎤 SuperWhisper is ready to record!

📁 Audio file: ${path.basename(audioPath)}
📋 File path copied to clipboard

**SuperWhisper should now be in Voice mode and ready to record.**

**To transcribe your audio:**
1. Import the audio file in SuperWhisper (drag & drop or File → Import)
2. Or record new audio directly in SuperWhisper
3. Copy the transcription and paste it here

**Or simply type your message below!** 👇`);
        });
      }
    });
  });
}

async function processWithWhisperCLI(audioPath) {
  return new Promise((resolve, reject) => {
    // Find the whisper executable
    const whisperPaths = [
      '/Users/<USER>/Library/Python/3.9/bin/whisper',
      '/usr/local/bin/whisper',
      '/opt/homebrew/bin/whisper'
    ];

    let whisperPath = null;
    for (const path of whisperPaths) {
      if (fs.existsSync(path)) {
        whisperPath = path;
        break;
      }
    }

    if (!whisperPath) {
      reject(new Error('OpenAI Whisper CLI not found. Please install with: pip install openai-whisper'));
      return;
    }

    // OpenAI Whisper CLI arguments
    const args = [
      audioPath,
      '--language', 'en',
      '--output_format', 'txt',
      '--output_dir', path.dirname(audioPath)
    ];

    console.log(`Using OpenAI Whisper CLI: ${whisperPath}`);
    console.log('Args:', args);

    const whisperProcess = spawn(whisperPath, args);

    let stdout = '';
    let stderr = '';

    whisperProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    whisperProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    whisperProcess.on('close', (code) => {
      console.log(`OpenAI Whisper CLI process closed with code:`, code);
      console.log('stdout:', stdout);
      console.log('stderr:', stderr);

      if (code === 0) {
        // OpenAI Whisper CLI creates a .txt file, try to read it
        const audioBasename = path.basename(audioPath, path.extname(audioPath));
        const txtFilePath = path.join(path.dirname(audioPath), `${audioBasename}.txt`);

        try {
          if (fs.existsSync(txtFilePath)) {
            const transcription = fs.readFileSync(txtFilePath, 'utf8').trim();
            console.log('OpenAI Whisper CLI transcription:', transcription);

            // Clean up the txt file
            fs.unlinkSync(txtFilePath);

            if (transcription) {
              resolve(transcription);
            } else {
              reject(new Error('Empty transcription from OpenAI Whisper CLI'));
            }
          } else {
            // Fallback: try to extract from stdout
            const transcription = extractTranscriptionFromOutput(stdout);
            if (transcription && transcription.trim()) {
              console.log('OpenAI Whisper CLI transcription from stdout:', transcription);
              resolve(transcription);
            } else {
              reject(new Error('No transcription file or output from OpenAI Whisper CLI'));
            }
          }
        } catch (fileError) {
          reject(new Error(`Failed to read transcription file: ${fileError.message}`));
        }
      } else {
        reject(new Error(`OpenAI Whisper CLI failed with code ${code}: ${stderr || 'No error details'}`));
      }
    });

    whisperProcess.on('error', (error) => {
      console.error('OpenAI Whisper CLI process error:', error);
      reject(new Error(`OpenAI Whisper CLI process error: ${error.message}`));
    });
  });
}

async function processWithMacWhisper(audioPath) {
  return new Promise((resolve, reject) => {
    // Use MacWhisper app directly
    const macWhisperPath = '/Applications/MacWhisper.app/Contents/MacOS/MacWhisper';

    if (!fs.existsSync(macWhisperPath)) {
      reject(new Error('MacWhisper app not found. Please install MacWhisper from the Mac App Store.'));
      return;
    }

    // Set a timeout to prevent hanging
    const TIMEOUT_MS = 30000; // 30 seconds timeout
    let timeoutId;
    let processCompleted = false;

    // Try different MacWhisper argument combinations
    const args = [
      audioPath,
      '--language', 'en',
      '--output_format', 'txt',
      '--print_colors', 'false',
      '--print_timestamps', 'false'
    ];

    console.log(`Using MacWhisper: ${macWhisperPath}`);
    console.log('Args:', args);

    const whisperProcess = spawn(macWhisperPath, args);

    // Set timeout
    timeoutId = setTimeout(() => {
      if (!processCompleted) {
        console.warn('MacWhisper timeout - killing process');
        whisperProcess.kill('SIGTERM');
        reject(new Error('MacWhisper timed out after 30 seconds. The audio file might be too long or MacWhisper is having issues.'));
      }
    }, TIMEOUT_MS);

    let stdout = '';
    let stderr = '';

    whisperProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    whisperProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    whisperProcess.on('close', (code) => {
      processCompleted = true;
      clearTimeout(timeoutId);

      console.log(`MacWhisper process closed with code:`, code);
      console.log('stdout:', stdout);
      console.log('stderr:', stderr);

      if (code === 0) {
        // Try to extract transcription from stdout first
        let transcription = extractTranscriptionFromOutput(stdout);

        if (!transcription || !transcription.trim()) {
          // MacWhisper might create output files instead
          const audioBasename = path.basename(audioPath, path.extname(audioPath));
          const possibleOutputFiles = [
            path.join(path.dirname(audioPath), `${audioBasename}.txt`),
            path.join(path.dirname(audioPath), `${audioBasename}.json`),
            path.join(process.cwd(), `${audioBasename}.txt`),
            path.join(process.cwd(), `${audioBasename}.json`)
          ];

          for (const outputFile of possibleOutputFiles) {
            try {
              if (fs.existsSync(outputFile)) {
                const content = fs.readFileSync(outputFile, 'utf8');
                transcription = content.trim();
                console.log(`Found MacWhisper output in: ${outputFile}`);
                // Clean up the file
                fs.unlinkSync(outputFile);
                break;
              }
            } catch (fileError) {
              console.warn(`Error reading ${outputFile}:`, fileError.message);
            }
          }
        }

        if (transcription && transcription.trim()) {
          console.log('MacWhisper transcription:', transcription);
          resolve(transcription);
        } else {
          // MacWhisper loaded successfully but produced no output
          reject(new Error('MacWhisper processed the file but produced no transcription. The audio might be too quiet, empty, or unclear.'));
        }
      } else {
        reject(new Error(`MacWhisper failed with code ${code}: ${stderr || 'No error details'}`));
      }
    });

    whisperProcess.on('error', (error) => {
      processCompleted = true;
      clearTimeout(timeoutId);
      console.error('MacWhisper process error:', error);
      reject(new Error(`MacWhisper process error: ${error.message}`));
    });
  });
}

function extractTranscriptionFromOutput(output) {
  // Extract transcription text from whisper output
  const lines = output.split('\n');
  let transcription = '';

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Skip empty lines, timestamps, and system messages
    if (!trimmedLine ||
        trimmedLine.includes('[') ||
        trimmedLine.includes('whisper_') ||
        trimmedLine.includes('ggml_') ||
        trimmedLine.includes('loading model') ||
        trimmedLine.includes('GPU') ||
        trimmedLine.includes('Metal') ||
        trimmedLine.includes('model size') ||
        trimmedLine.includes('compute buffer') ||
        trimmedLine.includes('kv ') ||
        trimmedLine.includes('-->') ||
        trimmedLine.match(/^\d+:\d+/)) {
      continue;
    }

    // This looks like actual transcription text
    if (trimmedLine.length > 3) {
      transcription += trimmedLine + ' ';
    }
  }

  return transcription.trim() || null;
}

async function generateWithOllama(message) {
  try {
    // Load configuration
    const config = JSON.parse(fs.readFileSync(path.join(__dirname, '../config.json'), 'utf8'));
    const ollamaConfig = config.ollama;

    // Create a conversational prompt
    const conversationalPrompt = `You are a friendly AI assistant. Please respond naturally and conversationally to the following message. Keep your response concise but engaging.

User: ${message}
Assistant: `;

    const response = await axios.post(`${ollamaConfig.url}/api/generate`, {
      model: ollamaConfig.model,
      prompt: conversationalPrompt,
      stream: false,
      options: {
        temperature: 0.7,
        top_p: 0.9,
        max_tokens: ollamaConfig.max_tokens || 500
      }
    }, {
      timeout: ollamaConfig.timeout || 30000
    });

    // Clean up the response by removing thinking tags and extra whitespace
    let cleanResponse = response.data.response.trim();

    // Remove <think>...</think> blocks
    cleanResponse = cleanResponse.replace(/<think>[\s\S]*?<\/think>/g, '');

    // Remove emojis for speech (but keep them for display)
    const speechText = cleanResponse.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '');

    // Clean up any extra whitespace
    cleanResponse = cleanResponse.trim();

    return { displayText: cleanResponse, speechText: speechText.trim() };
  } catch (error) {
    console.error('Ollama error:', error);
    if (error.code === 'ECONNREFUSED') {
      throw new Error('Ollama server is not running. Please start it with: ollama serve');
    }
    throw new Error('Failed to generate response with Ollama');
  }
}

async function generateSpeechWithSay(text) {
  try {
    // Load configuration
    const config = JSON.parse(fs.readFileSync(path.join(__dirname, '../config.json'), 'utf8'));
    const speechConfig = config.speech;

    // Create unique filename
    const timestamp = Date.now();
    const audioDir = path.join(__dirname, '../assets/audio');

    // Ensure audio directory exists
    if (!fs.existsSync(audioDir)) {
      fs.mkdirSync(audioDir, { recursive: true });
    }

    const audioPath = path.join(audioDir, `speech_${timestamp}.wav`);

    // Prepare say command
    const sayCommand = [
      'say',
      '-v', speechConfig.voice || 'Samantha',
      '-r', (speechConfig.rate || 200).toString(),
      '-o', audioPath,
      '--data-format=LEF32@22050',
      text
    ];

    // Execute say command
    return new Promise((resolve, reject) => {
      const sayProcess = spawn(sayCommand[0], sayCommand.slice(1));

      sayProcess.on('close', (code) => {
        if (code === 0 && fs.existsSync(audioPath)) {
          resolve(audioPath);
        } else {
          reject(new Error(`Say command failed with code ${code}`));
        }
      });

      sayProcess.on('error', (error) => {
        reject(new Error(`Say command error: ${error.message}`));
      });
    });

  } catch (error) {
    console.error('TTS error:', error);
    throw new Error('Failed to generate speech');
  }
}

async function generateLipSync(audioPath) {
  try {
    // Load configuration
    const config = JSON.parse(fs.readFileSync(path.join(__dirname, '../config.json'), 'utf8'));
    const rhubarbConfig = config.rhubarb;

    // Check if Rhubarb exists
    const rhubarbPath = path.join(__dirname, '..', rhubarbConfig.path);
    if (!fs.existsSync(rhubarbPath)) {
      throw new Error('Rhubarb Lip Sync not found. Please run setup script.');
    }

    // Create output path for lip sync data
    const timestamp = Date.now();
    const outputPath = path.join(__dirname, '../temp', `lipsync_${timestamp}.json`);

    // Ensure temp directory exists
    const tempDir = path.dirname(outputPath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Run Rhubarb
    return new Promise((resolve, reject) => {
      const rhubarbProcess = spawn(rhubarbPath, [
        '-f', 'json',
        '-o', outputPath,
        audioPath
      ]);

      let stderr = '';
      rhubarbProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      rhubarbProcess.on('close', (code) => {
        if (code === 0 && fs.existsSync(outputPath)) {
          try {
            const lipSyncData = JSON.parse(fs.readFileSync(outputPath, 'utf8'));

            // Convert Rhubarb format to our format
            const processedData = processMouthCues(lipSyncData);

            // Clean up temp file
            fs.unlinkSync(outputPath);

            resolve(processedData);
          } catch (parseError) {
            reject(new Error(`Failed to parse lip sync data: ${parseError.message}`));
          }
        } else {
          reject(new Error(`Rhubarb failed with code ${code}: ${stderr}`));
        }
      });

      rhubarbProcess.on('error', (error) => {
        reject(new Error(`Rhubarb process error: ${error.message}`));
      });
    });

  } catch (error) {
    console.error('Lip sync error:', error);
    throw new Error('Failed to generate lip sync data');
  }
}

function processMouthCues(rhubarbData) {
  // Convert Rhubarb mouth cues to our animation format
  const processedFrames = [];

  if (rhubarbData.mouthCues) {
    for (const cue of rhubarbData.mouthCues) {
      processedFrames.push({
        viseme: cue.value,
        timestamp: cue.start * 1000, // Convert to milliseconds
        duration: (cue.end - cue.start) * 1000,
        intensity: 1.0
      });
    }
  }

  return processedFrames;
}

async function generateAvatarAnimation(avatarFilename, lipSyncData, audioPath) {
  try {
    // Call Python server to generate animation frames
    const pythonServerUrl = 'http://localhost:5001';

    const response = await axios.post(`${pythonServerUrl}/generate-lip-sync`, {
      avatar_filename: avatarFilename,
      audio_path: audioPath,
      rhubarb_data: lipSyncData
    });

    if (response.data.success) {
      return response.data.animation_data;
    } else {
      throw new Error('Python server failed to generate animation');
    }

  } catch (error) {
    console.error('Avatar animation error:', error);
    // Return simplified animation data as fallback
    return lipSyncData.map((frame, index) => ({
      frame_index: index,
      viseme: frame.viseme,
      timestamp: frame.timestamp,
      intensity: frame.intensity
    }));
  }
}

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log('Make sure Ollama is running on port 11434');
});
